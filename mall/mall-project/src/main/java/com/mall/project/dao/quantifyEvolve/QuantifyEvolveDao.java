package com.mall.project.dao.quantifyEvolve;

import com.mall.project.service.bcSettings.BCSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 量化值进化量数据访问对象
 */
@Repository
public class QuantifyEvolveDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BCSettingsService bcSettingsService;

    /**
     * 计算量化值进化量
     */
    public void updateQuantifyEvolve() {
        // 只对用户类型为C的进行量化值进化量的计算
        String sql = "SELECT v.phone,v.value FROM quantization_value v,mall_b_users u WHERE v.phone = u.phone and u.user_type = 'C'\n" +
                "AND DATE(v.update_date) = CURDATE() - INTERVAL 1 DAY";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
        //查询C设置
        Map<String, Object> cSettings = bcSettingsService.getCSettings();
        // 合作企业各IDC设置 的开关
        String isEnabled = cSettings.get("isEnabled").toString();
        // C每ID每日自动量化值进化信用值%
        String quantifyToCredit = cSettings.get("quantifyToCredit").toString();
        if (isEnabled.equals("0")) {
            for (Map<String, Object> map : list) {
                String phone = (String) map.get("phone");
                //量化值
                BigDecimal value = new BigDecimal(map.get("value").toString());
                //信用值
                BigDecimal creditValue = value.multiply(new BigDecimal(quantifyToCredit)).setScale(2, BigDecimal.ROUND_DOWN).divide(new BigDecimal(100));
                //今日量化值进化量 = (量化值 - 信用值)
                BigDecimal quantifyEvolve = value.subtract(creditValue).setScale(2, BigDecimal.ROUND_DOWN);
                //使用 SELECT EXISTS 判断量化值进化量表中是否已经存在 CURDATE() - INTERVAL 1 DAY 的数据 如果存在则更新,不存在则插入
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantify_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY)", Integer.class, phone) == 0) {
                    sql = "INSERT INTO quantify_evolve(phone,quantify_evolve,quantify_evolve_total,update_date)VALUES(?,?,?,CURDATE() - INTERVAL 1 DAY)";
                    jdbcTemplate.update(sql, phone, quantifyEvolve, quantifyEvolve.add(new BigDecimal(sumQuantifyEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN));
                }else{
                    sql = "UPDATE quantify_evolve SET quantify_evolve = ?,quantify_evolve_total = ? WHERE phone = ? AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
                    jdbcTemplate.update(sql, quantifyEvolve, quantifyEvolve.add(new BigDecimal(sumQuantifyEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN), phone);
                }
            }
        }
    }

    /**
     * 统计 quantify_evolve ,phone 的和
     */
    public String sumQuantifyEvolve(String phone) {
        try{
            String sql = "SELECT SUM(quantify_evolve) FROM quantify_evolve WHERE phone = ? AND DATE(update_date) < CURDATE() - INTERVAL 1 DAY";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 查询量化值进化量,分页显示
     */
    public List<Map<String, Object>> queryQuantifyEvolvePages(String phone, String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT e.update_date,e.phone,u.username,e.quantify_evolve,e.quantify_evolve_total FROM quantify_evolve e LEFT JOIN mall_b_users u ON e.phone = u.phone WHERE 1= 1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND e.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND e.update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND e.update_date <= CURDATE() - INTERVAL 1 DAY";
        }
        sql += " ORDER BY e.update_date DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 量化值进化量总条数
     */
    public int totalQuantifyEvolve(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(1) FROM quantify_evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() - INTERVAL 1 DAY";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }
    /**
     * 化值进化量, 导出 Excel
     */
    public List<Map<String, Object>> exportQuantifyEvolveExcel(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT e.update_date,e.phone,u.username,e.quantify_evolve,e.quantify_evolve_total FROM quantify_evolve e LEFT JOIN mall_b_users u ON e.phone = u.phone WHERE 1= 1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND e.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND e.update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND e.update_date <= CURDATE() - INTERVAL 1 DAY";
        }
        sql += " ORDER BY e.update_date DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * 统计 今日总量化值进化量
     */
    public String todayTotalQuantifyEvolve(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT sum(quantify_evolve) as today_total_quantify_evolve FROM quantify_evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startTime);
        }else{
            sql += " AND update_date = CURDATE() - INTERVAL 1 DAY";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 统计 累计量化值进化量
     */
    public String totalQuantifyEvolve(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT sum(quantify_evolve_total) as total_quantify_evolve FROM quantify_evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(startTime);
        }else{
            sql += " AND update_date <= CURDATE() - INTERVAL 1 DAY";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 保存从mallB系统获取的量化值进化量数据
     */
    public void saveMallBQuantifyEvolveData(String phone, String quantifyEvolve, String updateDate) {
        // 检查当天是否已存在该手机号的数据
        String checkSql = "SELECT COUNT(1) FROM quantify_evolve WHERE phone = ? AND DATE(update_date) = ?";
        int count = jdbcTemplate.queryForObject(checkSql, Integer.class, phone, updateDate);

        if (count > 0) {
            // 如果存在，则更新
            String updateSql = "UPDATE quantify_evolve SET quantify_evolve = ?, quantify_evolve_total = ?, update_date = ? WHERE phone = ? AND DATE(update_date) = ?";
            jdbcTemplate.update(updateSql, quantifyEvolve, new BigDecimal(quantifyEvolve).add(new BigDecimal(sumQuantifyEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN), updateDate, phone, updateDate);
        } else {
            // 如果不存在，则插入
            String insertSql = "INSERT INTO quantify_evolve (phone, quantify_evolve, quantify_evolve_total, update_date) VALUES (?, ?, ?, ?)";
            jdbcTemplate.update(insertSql, phone, quantifyEvolve, new BigDecimal(quantifyEvolve).add(new BigDecimal(sumQuantifyEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN), updateDate);
        }
    }
}
