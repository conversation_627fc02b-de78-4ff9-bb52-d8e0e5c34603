package com.mall.project.dao.cooperateEnterprise;

import com.mall.project.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 用户数据访问层
 */
@Repository
@Slf4j
public class CooperateEnterpriseDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取所有合作企业
     */
    public List<Map<String, Object>> getAllCooperateEnterprise() {
        // 构造状态过滤查询语句，status=0表示有效合作企业
        String sql = "SELECT id,name FROM cooperate_enterprise where status = 0";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 添加已设置参数的企业信息
     */
    @Transactional
    public int addEnterpriseDataSet(Long enterpriseId){
        // 判断如果该企业已存在则 不能再添加
        Integer count = jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_data_set WHERE enterprise_id = ? and type = 0)", Integer.class, enterpriseId);
        if (count > 0) {
            throw new BusinessException("该企业已经存在不能重复添加!");
        }
        String sql = "select id,trade_name,trade_amount from enterprise_data_name where type = 0";
        List<Map<String, Object>>  dataMapList = jdbcTemplate.queryForList(sql);
        for (Map<String, Object> dataMap : dataMapList) {
            String tradeNameCountSql = "select trade_amount from enterprise_product_data where enterprise_id = ? and trade_name = ?";
            List<Map<String, Object>> tradeAmountList = jdbcTemplate.queryForList(tradeNameCountSql, enterpriseId, dataMap.get("trade_name"));
            if (tradeAmountList.isEmpty()) {
                continue;
            }
            String tradeAmount = java.util.Objects.toString(tradeAmountList.get(0).get("trade_amount"), "0");
            String updateSql = "update enterprise_data_name set trade_amount = trade_amount + "+Integer.parseInt(tradeAmount)+" where trade_name = ?  and type = 0";
            jdbcTemplate.update(updateSql, dataMap.get("trade_name"));
        }
        return jdbcTemplate.update("Insert into enterprise_data_set(enterprise_id,type,update_time)values(?,0,now())",enterpriseId);
    }
    /**
     * 删除已设置参数的企业信息
     */
    @Transactional
    public int deleteEnterpriseDataSet(Long enterpriseId){
        // 判断如果 enterpriseId 不存在则提示该企业ID 不存在
        Integer count = jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_data_set WHERE enterprise_id = ? and type = 0)", Integer.class, enterpriseId);
        if (count == 0) {
            throw new BusinessException("删除失败,该企业ID不存在!");
        }
        String sql = "select id,trade_name,trade_amount from enterprise_data_name where type = 0";
        List<Map<String, Object>>  dataMapList = jdbcTemplate.queryForList(sql);
        for (Map<String, Object> dataMap : dataMapList) {
            String tradeNameCountSql = "select trade_amount from enterprise_product_data where enterprise_id = ? and trade_name = ?";
            List<Map<String, Object>> tradeNameList = jdbcTemplate.queryForList(tradeNameCountSql, enterpriseId, dataMap.get("trade_name"));
            if (tradeNameList.isEmpty()) {
                /*tradeNameCountSql = "delete from enterprise_data_name where trade_name = ? and type = 0";
                jdbcTemplate.update(tradeNameCountSql, dataMap.get("trade_name"));*/
                continue;
            }
            // 修复空指针问题：使用Objects.toString()并设置默认值
            String tradeAmount = java.util.Objects.toString(tradeNameList.get(0).get("trade_amount"), "0");
            // 删除原空值检查逻辑（已通过Objects.toString处理）
            String updateSql = "update enterprise_data_name set trade_amount = trade_amount - "+Integer.parseInt(tradeAmount)+" where trade_name = ?  and type = 0";
            jdbcTemplate.update(updateSql, dataMap.get("trade_name"));
        }
        return jdbcTemplate.update("delete from enterprise_data_set where enterprise_id = ? and type = 0",enterpriseId);
    }
    /**
     * 查看已设置参数的企业信息
     */
    public List<Map<String, Object>> getEnterpriseDataSet(){
        String sql = "select s.enterprise_id,e.`name` from enterprise_data_set s, cooperate_enterprise e \n" +
                "where s.enterprise_id = e.id and type = 0";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 今日总交易量
     */
    public String todayTotalTrade(int type){
        try {
            String sql = "SELECT COALESCE(sum(today_total_trade), 0) as trade_amount from enterprise_trade_stats where enterprise_id in (select enterprise_id from enterprise_data_set where type = ?)  and DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
            return jdbcTemplate.queryForObject(sql, String.class, type);
        } catch (Exception e) {
            log.warn("查询今日总交易量失败: type={}", type, e);
            return "0";
        }
    }

    /**
     * 今日总合计数
     */
    public String todayTotalCount(int type){
        try {
            String sql = "SELECT COALESCE(sum(today_total_count), 0) as trade_amount from enterprise_trade_stats where enterprise_id in (select enterprise_id from enterprise_data_set where type = ?) AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
            return jdbcTemplate.queryForObject(sql, String.class, type);
        } catch (Exception e) {
            log.warn("查询今日总合计数失败: type={}", type, e);
            return "0";
        }
    }

    /**
     * 累计总交易量
     */
    public String cumulativeTotalTrade(int type){
        try {
            String sql = "SELECT COALESCE(sum(cumulative_total_trade), 0) as trade_amount from enterprise_trade_stats where enterprise_id in (select enterprise_id from enterprise_data_set where type = ?) and DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
            return jdbcTemplate.queryForObject(sql, String.class, type);
        } catch (Exception e) {
            log.warn("查询累计总交易量失败: type={}", type, e);
            return "0";
        }
    }

    /**
     * 删除企业交易数据
     */
    @Transactional
    public int deleteEnterpriseDataName(Long enterpriseProductDataId){
        // 判断如果 enterpriseDataId 不存在则提示该数据ID 不存在
        Integer count = jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_data_name WHERE id = ? and type = 0)", Integer.class, enterpriseProductDataId);
        if (count == 0) {
            throw new BusinessException("删除失败,该数据ID不存在!");
        }
        String sql = "delete from enterprise_data_name where id = ?";
        return jdbcTemplate.update(sql,enterpriseProductDataId);
    }

    /**
     * 设置自定义常数
     */
    @Transactional
    public int setCustomConstants(String customConstants){
        // 判断 Custom_constants 表 没有数据没有数据则插入数据,有数据再更新数据
        Integer count = jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM Custom_constants)", Integer.class);
        if (count == 0) {
            String insertSql = "Insert into Custom_constants(everyday_count,everyday_quantity,constants,result,Custom_constants_switch,update_time)values(0,0,1,0,0,now())";
            jdbcTemplate.update(insertSql);
        }
        String sql = "update Custom_constants set constants = ?,result = ?,update_time = NOW() where id = 1";
        double result = Double.parseDouble(jdbcTemplate.queryForMap("select everyday_count from Custom_constants where id = 1").get("everyday_count").toString()) / Integer.parseInt(customConstants);
        BigDecimal bd = new BigDecimal(Double.toString(result)).setScale(6, RoundingMode.DOWN);  // 使用 BigDecimal 直接截断（不四舍五入）
        double truncatedResult = bd.doubleValue();
        return jdbcTemplate.update(sql, customConstants, truncatedResult);
    }
    /**
     * 查询自定义常数
     */
    public Map<String, Object> getCustomConstants(){
        return jdbcTemplate.queryForMap("select constants,result,Custom_constants_switch,enterprise_data_switch,quantity_data_switch from Custom_constants where id = 1");
    }

    /**
     * 添加各企业系统每日更新总累计量化数企业名称
     */
    @Transactional
    public int addEnterpriseQuantitySet(Long enterpriseId){
        // 判断如果该企业已存在则 不能再添加
        Integer count = jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_data_set WHERE enterprise_id = ? and type = 1)", Integer.class, enterpriseId);
        if (count > 0) {
            throw new BusinessException("该企业已经存在不能重复添加!");
        }
        String sql = "select id,trade_name,trade_amount from enterprise_data_name where type = 1";
        List<Map<String, Object>>  dataMapList = jdbcTemplate.queryForList(sql);
        for (Map<String, Object> dataMap : dataMapList) {
            String tradeNameCountSql = "select trade_amount from enterprise_product_data where enterprise_id = ? and trade_name = ?";
            List<Map<String, Object>> tradeAmountList = jdbcTemplate.queryForList(tradeNameCountSql, enterpriseId, dataMap.get("trade_name"));
            if (tradeAmountList.isEmpty()) {
                continue;
            }
            String tradeAmount = java.util.Objects.toString(tradeAmountList.get(0).get("trade_amount"), "0");
            String updateSql = "update enterprise_data_name set trade_amount = trade_amount + "+Integer.parseInt(tradeAmount)+" where trade_name = ? and type = 1";
            jdbcTemplate.update(updateSql, dataMap.get("trade_name"));
        }
        return jdbcTemplate.update("Insert into enterprise_data_set(enterprise_id,type,update_time)values(?,1,now())",enterpriseId);
    }
    /**
     * 删除已添加各企业系统每日更新总累计量化数企业名称
     */
    @Transactional
    public int deleteEnterpriseQuantitySet(Long enterpriseId){
        // 判断如果 enterpriseId 不存在则提示该企业ID 不存在
        Integer count = jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_data_set WHERE enterprise_id = ? and type = 1)", Integer.class, enterpriseId);
        if (count == 0) {
            throw new BusinessException("删除失败,该企业ID不存在!");
        }
        String sql = "select id,trade_name,trade_amount from enterprise_data_name where type = 1";
        List<Map<String, Object>>  dataMapList = jdbcTemplate.queryForList(sql);
        for (Map<String, Object> dataMap : dataMapList) {
            String tradeNameCountSql = "select trade_amount from enterprise_product_data where enterprise_id = ? and trade_name = ?";
            List<Map<String, Object>> tradeNameList = jdbcTemplate.queryForList(tradeNameCountSql, enterpriseId, dataMap.get("trade_name"));
            if (tradeNameList.isEmpty()) {
                tradeNameCountSql = "delete from enterprise_data_name where trade_name = ? and type = 1";
                jdbcTemplate.update(tradeNameCountSql, dataMap.get("trade_name"));
                continue;
            }
            String tradeAmount = java.util.Objects.toString(tradeNameList.get(0).get("trade_amount"), "0");
            String updateSql = "update enterprise_data_name set trade_amount = trade_amount - "+Integer.parseInt(tradeAmount)+" where trade_name = ?  and type = 1";
            jdbcTemplate.update(updateSql, dataMap.get("trade_name"));
        }
        return jdbcTemplate.update("delete from enterprise_data_set where enterprise_id = ? and type = 1",enterpriseId);
    }

    /**
     * 查看已添加各企业系统每日更新总累计量化数企业名称
     */
    public List<Map<String, Object>> getEnterpriseQuantitySet(){
        String sql = "select s.enterprise_id,e.`name` from enterprise_data_set s, cooperate_enterprise e \n" +
                "where s.enterprise_id = e.id and type = 1";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 删除各企业系统每日更新总累计量化数交易数据
     */
    @Transactional
    public int deleteEnterpriseQuantityName(Long enterpriseProductDataId){
        // 判断如果 enterpriseDataId 不存在则提示该数据ID 不存在
        Integer count = jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_data_name WHERE id = ? and type = 1)", Integer.class, enterpriseProductDataId);
        if (count == 0) {
            throw new BusinessException("删除失败,该数据ID不存在!");
        }
        String sql = "delete from enterprise_data_name where id = ?";
        return jdbcTemplate.update(sql,enterpriseProductDataId);
    }

    /**
     * 计算量化率 quantizationRate
     */
    @Transactional
    public List<Map<String,Object>> quantizationRate(String searchMonth){
        // 计算量化率
        String sql = "SELECT CURDATE() - INTERVAL 1 DAY as TODAY,TRUNCATE(COALESCE(result,0) / NULLIF(COALESCE(everyday_quantity,0), 0), 6) AS quantization_rate FROM Custom_constants";
        List<Map<String, Object>> mapsList = jdbcTemplate.queryForList(sql);
        if (!mapsList.isEmpty()) {
            // 添加空值校验，设置默认值
            Object rateObj = mapsList.get(0).get("quantization_rate");
            Double rate;
            if (rateObj == null) {
                rate = 0.0;
            } else if (rateObj instanceof BigDecimal) {
                rate = ((BigDecimal) rateObj).doubleValue();
            } else if (rateObj instanceof Double) {
                rate = (Double) rateObj;
            } else {
                // 尝试从字符串转换
                rate = Double.parseDouble(rateObj.toString());
            }
            //判断如果 quantify_date 存在则更新，不存在则插入
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM quantization_rate WHERE quantify_date = ?", Integer.class, mapsList.get(0).get("TODAY"));
            if (count > 0) {
                sql = "UPDATE quantization_rate SET quantify_rate = ?,create_time = NOW() WHERE quantify_date = ?";
            }else {
                sql = "INSERT INTO quantization_rate(quantify_rate,quantify_date,create_time)VALUES(?,?,NOW())";
            }
            jdbcTemplate.update(sql, rate,mapsList.get(0).get("TODAY"));
        }
        // 查询表当月的 quantization_rate 数据
        String selectSql;
        if (searchMonth == null || searchMonth.trim().isEmpty()) {
            // 如果 searchMonth 为空，查询当月数据
            selectSql = "SELECT quantify_date as quantifyDate,quantify_rate as quantizationRate FROM quantization_rate WHERE DATE_FORMAT(quantify_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')";
            return jdbcTemplate.queryForList(selectSql);
        } else {
            // 如果 searchMonth 不为空，按照指定月份查询数据
            selectSql = "SELECT quantify_date as quantifyDate,quantify_rate as quantizationRate FROM quantization_rate WHERE DATE_FORMAT(quantify_date, '%Y-%m') = ?";
            return jdbcTemplate.queryForList(selectSql,searchMonth);
        }
    }

    /**
     * 将合作企业各IDB设置 显示到量化率页面的量化值进化量,信用值进化量
     */
    public Map<String, Object> getBSettings() {
        String sql = "SELECT quantify_to_credit,credit_to_coupon FROM BC_settings WHERE type = 'B'";
        return jdbcTemplate.queryForMap(sql);
    }

    /**
     * 交易数据设置
     */
    public int tradeDataSet(Long enterpriseId, String tradeName){
        try {
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM trade_data_set WHERE enterprise_id = ? and data_name = ?", Integer.class, enterpriseId,tradeName);
            if (count > 0) {
                 return 0;
            }else{
                String sql = "INSERT INTO trade_data_set(enterprise_id,data_name,update_time) VALUES(?,?,NOW())";
                return jdbcTemplate.update(sql, enterpriseId, tradeName);
            }
        } catch (Exception e) {
            throw new BusinessException("交易数据名称新增失败: " + e.getMessage());
        }
    }
    /**
     * 查询交易数据名称
     */
    public List<Map<String, Object>> tradeDataSet(){
        try {
            String sql = "SELECT id,enterprise_id,data_name,update_time FROM trade_data_set";
            return jdbcTemplate.queryForList(sql);
        } catch (EmptyResultDataAccessException e) {
            throw new BusinessException("查询交易数据名称失败: 未查询到任何记录");
        } catch (Exception e) {
            throw new BusinessException("查询交易数据名称失败: " + e.getMessage());
        }
    }

    /**
     * 删除数据名称
     */
    public int deleteTradeDataSet(Long id){
        try {
            String sql = "DELETE FROM trade_data_set WHERE id = ?";
            return jdbcTemplate.update(sql, id);
        } catch (Exception e) {
            throw new BusinessException("删除数据名称失败: " + e.getMessage());
        }
    }

    // 用于去B 系统读取每日的交易数据 类型1:广告 2:技术引流 3:其他
    public String queryTradeDataSet(){
        try  {
            String sql = "SELECT GROUP_CONCAT(\n" +
                    "    CASE \n" +
                    "      WHEN data_name = '权限开通' THEN 1 \n" +
                    "        WHEN data_name = '平台广告' THEN 2  \n" +
                    "        WHEN data_name = '技术引流' THEN 3 \n" +
                    "        WHEN data_name = '开通代销' THEN 4 \n" +
                    "        ELSE 5 \n" +
                    "    END\n" +
                    "    SEPARATOR ','\n" +
                    ") AS types\n" +
                    "FROM trade_data_set";
            return jdbcTemplate.queryForObject(sql, String.class);
        } catch (Exception e) {
            throw new BusinessException("查询交易数据设置失败: " + e.getMessage());
        }
    }
    /**
     * 设置交易数据的每笔交易金额
     */
    @Transactional
    public int tradeDataParameterSet(Long enterpriseId, String perTradeAmount,String onOff){
        try {
            // Check if the record for enterpriseId already exists
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM trade_data_Parameter_set WHERE enterprise_id = ?", Integer.class, enterpriseId);

            if (count > 0) {
                // If record exists, update it
                String updateSql = "UPDATE trade_data_Parameter_set SET per_trade_amount = ?, on_off = ?, update_time = NOW() WHERE enterprise_id = ?";
                jdbcTemplate.update(updateSql, perTradeAmount, onOff, enterpriseId);
            } else {
                // If record does not exist, insert a new one
                String insertSql = "INSERT INTO trade_data_Parameter_set(enterprise_id, per_trade_amount, on_off, update_time) VALUES(?,?,?,NOW())";
                jdbcTemplate.update(insertSql, enterpriseId, perTradeAmount, onOff);
            }
            if (onOff.equals("0")) {  //开启
                String updateSql = "UPDATE enterprise_product_data SET total_count = trade_amount * ? WHERE enterprise_id = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
                jdbcTemplate.update(updateSql, Double.parseDouble(perTradeAmount) / 100, enterpriseId);
            }else if(onOff.equals("1")){ //关闭
                String updateSql  = "update enterprise_product_data set total_count = ? where enterprise_id = ? and date(update_time) = CURDATE() - INTERVAL 1 DAY";
                jdbcTemplate.update(updateSql, Double.parseDouble(perTradeAmount) / 100, enterpriseId);
            }
            return 1;
        } catch (Exception e) {
            throw new BusinessException("设置交易数据的每笔交易金额失败: " + e.getMessage());
        }
    }

    /**
     * 查询设置交易数据的每笔交易金额
     */
    public Map<String, Object> QueryTradeDataParameterSet(){
        String sql = "SELECT id,enterprise_id,per_trade_amount,on_off,update_time FROM trade_data_Parameter_set";
        List<Map<String, Object>> mapsList = jdbcTemplate.queryForList(sql);
        if (!mapsList.isEmpty()) {
            return mapsList.get(0);
        } else {
            return null;
        }
    }
    /**
     * 查询中南惠 交易数据明细
     */
    public List<Map<String, Object>> QueryZNHTradeDataPages(String phone, String startTime, String endTime, int limit, int offset){
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT DATE(d.update_time) as update_time, d.phone, d.company_name as enterprise_name, d.trade_name, d.trade_amount, d.total_count FROM enterprise_product_data d WHERE d.status = 0");

        // 用于存储查询参数的列表
        List<Object> params = new ArrayList<>();

        if(phone != null && !phone.isEmpty()){
            // 使用like模糊查询
            sqlBuilder.append(" AND d.phone LIKE ?");
            params.add("%" + phone + "%");
        }

        // 添加 startTime 条件（如果提供）
        if (startTime != null && !startTime.isEmpty()) {
            sqlBuilder.append(" AND DATE(d.update_time) = ?");
            params.add(startTime);
        }else{
            sqlBuilder.append(" AND DATE(d.update_time) <= CURDATE() - INTERVAL 1 DAY");
        }

        sqlBuilder.append(" ORDER BY update_time desc LIMIT ? OFFSET ?");
        params.add(limit);
        params.add(offset);
        // 执行查询
        return jdbcTemplate.queryForList(sqlBuilder.toString(), params.toArray());
    }
    // 统计总数
    public int countZNHTradeDatas(String phone, String startTime, String endTime) {
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM enterprise_product_data WHERE status = 0");
        if (phone != null) {
            sql.append(" AND phone LIKE ?");
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql.append(" AND DATE(update_time) = ?");
            params.add(startTime);
        }else{
            sql.append(" AND DATE(update_time) <= CURDATE() - INTERVAL 1 DAY");
        }
        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }
    // 统计今日总交易量
    public double sumTradeAmount(String phone, String startTime) {
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT SUM(trade_amount) FROM enterprise_product_data WHERE status = 0");
        if (isNotEmpty(phone)) {
            sql.append(" AND phone LIKE ?");
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql.append(" AND DATE(update_time) = ?");
            params.add(startTime);
        }else{
            sql.append(" AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY");
        }
        Double result = jdbcTemplate.queryForObject(sql.toString(), Double.class, params.toArray());
        return result != null ? result : 0.0; // 添加null值处理
    }
    // 统计中南惠今日总合计数
    public double sumTotalCount(String phone, String startTime) {
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT SUM(total_count) FROM enterprise_product_data WHERE status = 0");
        if (isNotEmpty(phone)) {
            sql.append(" AND phone LIKE ?");
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql.append(" AND DATE(update_time) = ?");
            params.add(startTime);
        }else{
            sql.append(" AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY");
        }
        Double result = jdbcTemplate.queryForObject(sql.toString(), Double.class, params.toArray());
        return result != null ? result : 0.0;
    }
    // 导出ZNH交易数据
    public List<Map<String, Object>> zNHTradeDataExport(String phone, String startTime) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT DATE(d.update_time) as update_time, d.phone, d.company_name as enterprise_name, d.trade_name, d.trade_amount, d.total_count FROM enterprise_product_data d WHERE d.status = 0");

        // 用于存储查询参数的列表
        List<Object> params = new ArrayList<>();

        if(phone != null && !phone.isEmpty()){
            // 使用like模糊查询
            sqlBuilder.append(" AND d.phone LIKE ?");
            params.add("%" + phone + "%");
        }

        // 添加 startTime 条件（如果提供）
        if (startTime != null && !startTime.isEmpty()) {
            sqlBuilder.append(" AND DATE(d.update_time) = ?");
            params.add(startTime);
        }else{
            sqlBuilder.append(" AND DATE(d.update_time) <= CURDATE() - INTERVAL 1 DAY");
        }
        sqlBuilder.append(" ORDER BY update_time desc");
        // 执行查询
        return jdbcTemplate.queryForList(sqlBuilder.toString(), params.toArray());
    }
    //saveMallBTradeData
    public void saveMallBTradeData(String enterpriseId, String phone, String companyName,String tradeName, String tradeAmount) {
        //log.info("开始处理并存储mallB交易数据: enterpriseId={}, phone={}, companyName={}, tradeName={}, tradeAmount={}", enterpriseId, phone, companyName, tradeName, tradeAmount);
        // 判断 如果 enterpriseId、phone、companyName、tradeName、payTime 已经在 enterprise_product_data 表中存在,则更新而不插入
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_product_data WHERE enterprise_id = ? AND phone = ? AND company_name = ? AND trade_name = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY)", Integer.class, enterpriseId, phone, companyName, tradeName) > 0) {
            String sql = "UPDATE enterprise_product_data SET trade_amount = ? WHERE enterprise_id = ? AND phone = ? AND company_name = ? AND trade_name = ? AND update_time = CURDATE() - INTERVAL 1 DAY";
            jdbcTemplate.update(sql, tradeAmount, enterpriseId, phone, companyName, tradeName);
        }else {
            String sql = "INSERT INTO enterprise_product_data (enterprise_id, phone, company_name, trade_name, trade_amount, update_time) VALUES (?, ?, ?, ?, ?, CURDATE() - INTERVAL 1 DAY)";
            jdbcTemplate.update(sql, enterpriseId, phone, companyName, tradeName, tradeAmount);
        }
    }
    public void updateEnterpriseTradeData(){
        String sql = "select enterprise_id,per_trade_amount,on_off from  trade_data_Parameter_set";
        List<Map<String, Object>> mapsList = jdbcTemplate.queryForList(sql);
        if (mapsList.isEmpty()) {
            throw new BusinessException("未找到交易参数配置!");
        }
        for (Map<String, Object> map : mapsList) {
            String enterpriseId = map.get("enterprise_id").toString();
            String perTradeAmount = map.get("per_trade_amount").toString();
            String onOff = map.get("on_off").toString();
            if (onOff.equals("0")) {  //开启
                String updateSql = "UPDATE enterprise_product_data SET total_count = trade_amount * ? WHERE enterprise_id = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
                jdbcTemplate.update(updateSql, Double.parseDouble(perTradeAmount) / 100, enterpriseId);
            }else if(onOff.equals("1")){ //关闭
                sql = "update enterprise_product_data set total_count = trade_amount where enterprise_id = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
                jdbcTemplate.update(sql, Double.parseDouble(perTradeAmount) / 100, enterpriseId);
            }
        }
        //更新昨日交易数据
        sql = "SELECT \n" +
                "  enterprise_id,\n" +
                "  SUM(CASE \n" +
                "        WHEN status = '0' \n" +
                "             AND update_time >= CURDATE() - INTERVAL 1 DAY \n" +
                "             AND update_time < CURDATE() \n" +
                "        THEN CAST(NULLIF(COALESCE(trade_amount, '0'), '') AS DECIMAL(20,2)) \n" +
                "        ELSE 0 \n" +
                "      END) AS yesterday_trade_amount,\n" +
                "  SUM(CASE \n" +
                "        WHEN status = '0' \n" +
                "             AND update_time >= CURDATE() - INTERVAL 1 DAY \n" +
                "             AND update_time < CURDATE() \n" +
                "        THEN CAST(NULLIF(COALESCE(total_count, '0'), '') AS DECIMAL(20,2)) \n" +
                "        ELSE 0 \n" +
                "      END) AS yesterday_total_count,\n" +
                "  SUM(CASE \n" +
                "        WHEN status = '0' \n" +
                "        THEN CAST(NULLIF(COALESCE(trade_amount, '0'), '') AS DECIMAL(18,2)) \n" +
                "        ELSE 0 \n" +
                "      END) AS trade_amount_total\n" +
                "FROM enterprise_product_data\n" +
                "GROUP BY enterprise_id";
        List<Map<String, Object>> dataLists = jdbcTemplate.queryForList(sql);
        for (Map<String, Object> datas : dataLists){
            String enterprise_id = datas.get("enterprise_id").toString();
            String yesterday_trade_amount = datas.get("yesterday_trade_amount").toString();
            String yesterday_total_count = datas.get("yesterday_total_count").toString();
            String trade_amount_total = datas.get("trade_amount_total").toString();
            // 判断如果昨日交易数据不存在，则更新而不插入 enterprise_id、update_time
            if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_trade_stats WHERE enterprise_id = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY)", Integer.class, enterprise_id) > 0) {
                sql = "UPDATE enterprise_trade_stats SET today_total_trade = ?,today_total_count = ?,cumulative_total_trade = ? WHERE enterprise_id = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
                jdbcTemplate.update(sql, yesterday_trade_amount, yesterday_total_count, trade_amount_total, enterprise_id);
            }else{
                sql = "INSERT INTO enterprise_trade_stats(enterprise_id,today_total_trade,today_total_count,cumulative_total_trade,update_time)VALUES(?,?,?,?,CURDATE() - INTERVAL 1 DAY + INTERVAL 5 MINUTE)";
                jdbcTemplate.update(sql, enterprise_id, yesterday_trade_amount, yesterday_total_count, trade_amount_total);
            }
        }
    }
    //企业交易数据读取, 开、关
    @Transactional
    public int setEnterpriseDataSwitch(String enterpriseDataSwitch){
        try{
            String sql = "";
            if(enterpriseDataSwitch.equals("1")){   // 关闭
                sql = "UPDATE Custom_constants SET enterprise_data_switch = 1,everyday_count = 0";
            }else{
                sql = "SELECT sum(trade_amount) as trade_amount from enterprise_data_name where type = 0 and DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
                List<Map<String, Object>> mapsList = jdbcTemplate.queryForList(sql);
                if (!mapsList.isEmpty()) {
                    Object tradeAmountObj = mapsList.get(0).get("trade_amount");
                    String trade_amount = "0"; // 默认值
                    if (tradeAmountObj != null) {
                        trade_amount = tradeAmountObj.toString();
                    }
                    sql = "UPDATE Custom_constants SET everyday_count = ?,enterprise_data_switch = 0";
                    jdbcTemplate.update(sql, trade_amount);
                }
            }
            return  1;
        }catch (Exception e){
            log.error("设置失败: {}", e.getMessage(), e);
            throw new BusinessException("设置失败: " + e.getMessage());
        }
    }
    // 各企业系统每日更新总累计量化数 开关
    @Transactional
    public int setQuantityDataSwitch(String quantityDataSwitch){
        try{
            String sql = "";
            if(quantityDataSwitch.equals("1")){   // 关闭
                sql = "UPDATE Custom_constants SET quantity_data_switch = 1,everyday_quantity = 0";
            }else{
                sql = "SELECT sum(trade_amount) as trade_amount from enterprise_data_name where type = 1 and DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
                List<Map<String, Object>> mapsList = jdbcTemplate.queryForList(sql);
                if (!mapsList.isEmpty()) {
                    String trade_amount = mapsList.get(0).get("trade_amount").toString();
                    sql = "UPDATE Custom_constants SET everyday_quantity = ?,quantity_data_switch = 0";
                    jdbcTemplate.update(sql, trade_amount);
                }
            }
            return 1;
        }catch (Exception e) {
            throw new BusinessException("设置失败: " + e.getMessage());
        }
    }
    //自定义常数开、关
    public int setCustomConstantsSwitch(String customConstantsSwitch){
        try{
            String sql = "";
            if(customConstantsSwitch.equals("1")){   // 关闭
                sql = "UPDATE Custom_constants SET Custom_constants_switch = 1";
            }else{
                sql = "UPDATE Custom_constants SET Custom_constants_switch = 0";
            }
            return jdbcTemplate.update(sql);
        }catch (Exception e) {
            throw new BusinessException("设置失败: " + e.getMessage());
        }
    }
}

