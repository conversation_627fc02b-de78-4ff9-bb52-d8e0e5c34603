package com.mall.project.dao.dailyTradePercentage;

import com.mall.project.dto.dailyTradePercentage.DailyTradePercentage;
import com.mall.project.util.PreparedStatementUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 所有企业各ID每日每笔新交易数据的量化数比配置
 */

@Repository
@Slf4j
public class DailyTradePercentageDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取所有企业各ID每日每笔新交易数据的量化数比配置
     */
    public Map<String,Object> getDailyTradePercentage(){
        String sql = "SELECT is_enabled,daily_trade_percentage,ranking1,ranking1_percentage,ranking2,ranking2_percentage FROM daily_trade_percentage WHERE id = 1";
        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (EmptyResultDataAccessException e) {
            // 当数据库中没有数据时，返回空的Map，让Service层处理
            return new HashMap<>();
        }
    }
    //按 所有企业各ID每日每笔新交易数据的量化数比 更新企业产品数据今日量化数
    public void updateEnterpriseProductData(BigDecimal dailyTradePercentage){
        try{
            String sql = "UPDATE mall_b_users_count b\n" +
                    "JOIN (\n" +
                    "    SELECT \n" +
                    "        phone, \n" +
                    "        SUM(CAST(total_count AS DECIMAL(10,2))) * ? AS sum_total\n" +
                    "    FROM enterprise_product_data\n" +
                    "    WHERE update_time >= CURDATE() - INTERVAL 1 DAY \n" +
                    "      AND update_time < CURDATE()\n" +
                    "    GROUP BY phone\n" +
                    ") d ON b.phone = d.phone\n" +
                    "SET b.quantify = d.sum_total\n" +
                    "WHERE b.update_time >= CURDATE() - INTERVAL 1 DAY \n" +
                    "  AND b.update_time < CURDATE()";
            jdbcTemplate.update(sql, dailyTradePercentage.divide(new BigDecimal(100)));
        }catch (Exception e){
            log.error("更新企业产品数据失败: {}", e.getMessage(), e);
        }
    }
    // 按 排位 更新企业产品数据今日量化数
    public void updateEnterpriseProductDataByRanking(Integer ranking, BigDecimal rankingPercentage){
        try{
            String sql = "SELECT d.phone,d.total_count,u.jurisdiction FROM enterprise_product_data d,mall_b_users u WHERE d.phone = u.phone AND DATE(d.update_time) = CURDATE() - INTERVAL 1 DAY";
            List<Map<String, Object>> mapsList = jdbcTemplate.queryForList(sql);
            for (Map<String, Object> map : mapsList) {
                String phone = map.get("phone").toString();
                int jurisdiction = Integer.parseInt(map.get("jurisdiction").toString());   // 原用户权限：权限1：1，权限2：2，权限3：3
                int willCompareJurisdiction = 0;
                String upRankingPhone = "";
                boolean skipCurrentIteration = false;
                do{
                    Map<String, Object> levelDataMap = judgeUserLevel(phone, ranking);
                    if (levelDataMap.isEmpty()) {
                        skipCurrentIteration = true;  // 设置标志，跳过当前循环
                        break;
                    }
                    // 判断权限 jurisdiction 如果小于要比较的权限，则跳过
                    willCompareJurisdiction = Integer.parseInt(levelDataMap.get("jurisdiction").toString());
                    upRankingPhone = levelDataMap.get("phone").toString();
                    ranking += 1;
                }while(willCompareJurisdiction < jurisdiction);
                
                if (skipCurrentIteration) {
                    continue; // 跳到下一次for循环
                }
                BigDecimal totalCount = new BigDecimal(map.get("total_count").toString());
                BigDecimal quantifyCount = totalCount.multiply(rankingPercentage.divide(new BigDecimal(100)));
                String updateSql = "UPDATE mall_b_users_count set quantify = quantify + ? where phone = ? and DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
                jdbcTemplate.update(updateSql, quantifyCount, upRankingPhone);
            }
        }catch (Exception e){
            log.error("更新企业产品数据失败: {}", e.getMessage(), e);
        }
    }
    /**
     * 判断用户层级
     * 用户层级关系查询业务
     * 核心功能
     * 这个SQL实现了一个用户层级关系向上追溯查询的业务逻辑，主要用于在B端用户体系中查找指定用户的上级层级关系。
     * 业务逻辑说明
     * 起始点：从指定手机号的用户开始作为Level 0（自己）
     * 向上递归：通过parent_id字段向上查找父级用户，构建完整的上级层级链条
     * Level 0：查询起始用户（自己）
     * Level 1：直接上级
     * Level 2：上级的上级
     * Level 3：上上级的上级
     * 以此类推...
     * 用户过滤：只查询符合条件的B端用户
     * 状态正常（status = 0）
     * 链类型为B（chain_type = 'B'）
     * 用户类型为B或CB（user_type = 'B' 或 user_type = 'CB'）
     * 营业执照去重：关键业务规则
     * 如果多个用户使用相同的营业执照（business_license）
     * 只保留其中注册时间最早的用户（create_time最小）
     * 过滤掉同营业执照下的其他用户
     * 层级重新计算：去重后重新计算层级编号，保持层级关系的连续性
     * 结果筛选：返回指定层级（如Level 3）的用户信息
     */
    public Map<String, Object> judgeUserLevel(String phone, Integer ranking){
        String sql = "SELECT * FROM (\n" +
                "    WITH RECURSIVE user_hierarchy AS (\n" +
                "        SELECT \n" +
                "            id, \n" +
                "            parent_id, \n" +
                "            0 AS level\n" +
                "        FROM mall_b_users\n" +
                "        WHERE phone = ? \n" +
                "          AND status = 0 \n" +
                "          AND chain_type = 'B' \n" +
                "          AND user_type IN ('B', 'CB') \n" +
                "        \n" +
                "        UNION ALL\n" +
                "        \n" +
                "        SELECT \n" +
                "            u.id, \n" +
                "            u.parent_id, \n" +
                "            uh.level + 1\n" +
                "        FROM mall_b_users u\n" +
                "        JOIN user_hierarchy uh ON u.id = uh.parent_id\n" +
                "        WHERE uh.level < 10\n" +
                "          AND u.status = 0 \n" +
                "          AND u.chain_type = 'B' \n" +
                "          AND user_type IN ('B', 'CB') \n" +
                "    ),\n" +
                "    full_hierarchy AS (\n" +
                "        SELECT u.*, uh.level\n" +
                "        FROM user_hierarchy uh\n" +
                "        JOIN mall_b_users u ON uh.id = u.id\n" +
                "    ),\n" +
                "    deduplicated_hierarchy AS (\n" +
                "        SELECT *\n" +
                "        FROM full_hierarchy fh1\n" +
                "        WHERE fh1.create_time = (\n" +
                "            SELECT MIN(fh2.create_time)\n" +
                "            FROM full_hierarchy fh2\n" +
                "            WHERE fh2.business_license = fh1.business_license\n" +
                "        )\n" +
                "    )\n" +
                "    SELECT \n" +
                "        id, phone, parent_username, status, flag, create_time, \n" +
                "        jurisdiction, username, fans, address, login_address,\n" +
                "        ROW_NUMBER() OVER (ORDER BY level) - 1 AS level\n" +
                "    FROM deduplicated_hierarchy\n" +
                "    ORDER BY level\n" +
                ") z \n" +
                " WHERE z.level = ?";
        Map<String, Object> levelDataMap;
        try {
            levelDataMap = jdbcTemplate.queryForMap(sql, phone, ranking);
        } catch (EmptyResultDataAccessException e) {
            // 查询结果为空时返回空Map
            levelDataMap = new HashMap<>();
        }
        return levelDataMap;
    }

    /**
     * 保存或更新所有企业各ID每日每笔新交易数据的量化数比配置
     */
    public int saveOrUpdateDailyTradePercentage(DailyTradePercentage pojo, Integer updatePerson){
        try {
            String sql = "INSERT INTO daily_trade_percentage(id, is_enabled, daily_trade_percentage, ranking1, ranking1_percentage, ranking2, ranking2_percentage, update_person, update_time) VALUES (1, ?, ?, ?, ?, ?, ?, ?, NOW()) " +
                    "ON DUPLICATE KEY UPDATE " +
                    "is_enabled = VALUES(is_enabled), daily_trade_percentage = VALUES(daily_trade_percentage), ranking1 = VALUES(ranking1), ranking1_percentage = VALUES(ranking1_percentage), ranking2 = VALUES(ranking2), ranking2_percentage = VALUES(ranking2_percentage), update_time = NOW()";
            jdbcTemplate.update(connection -> {
                var ps = connection.prepareStatement(sql);
                try {
                    PreparedStatementUtil.setPreparedStatementParams(ps, pojo, updatePerson);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                return ps;
            });
            return 1;
        } catch (Exception e) {
            return 0;
        }
    }
}
